# -*- coding: utf-8 -*-
"""
-------------------------------------------------
@version    : v1.0
<AUTHOR> fangzheng
@contact    : <EMAIL>
@software   : PyCharm
@filename   : screenshot_service.py
@create time: 2023/6/27 12:14 PM
@modify time: 2023/6/27 12:14 PM
@describe   : windows截图服务
-------------------------------------------------
1、该服务只能部署在windows服务器上，因为需要依赖windows的dll库来实现对Excel内容截图功能

"""

import os
import glob
from flask import Flask, request, Response, jsonify
import re
import time
import uuid
import json
import base64
import pythoncom
import excel2img
import pywintypes
from openpyxl import load_workbook
import subprocess
import win32com.client as win32
import socket
import threading
import shutil
from datetime import datetime

# win32com.client.Dispatch('Word.Application')
app = Flask(__name__)
opd = os.path.dirname
curr_path = opd(os.path.realpath(__file__))

def print2(msg):
    print("当前时间:{dt}".format(dt=datetime.now().strftime("%Y-%m-%d %H:%M:%S")),msg )

# Excel操作锁
excel_lock = threading.Lock()


@app.route("/upload", methods=["POST"])
def save_file():
    data = request.files
    field = request.form.get('field')
    file = data['files']  # 获取

    if not file.filename.endswith('.xlsx'):
        return "文件格式不正确\n"
    
    # 创建唯一临时工作目录
    temp_dir = f"temp_{uuid_generate_code()}"
    os.makedirs(temp_dir, exist_ok=True)

    # 保存文件到独立目录
    unique_excel_path = os.path.join(temp_dir, f"{uuid_generate_code()}_{file.filename}")
    file.save(unique_excel_path)
    print2(f"\n\n================{unique_excel_path}================")

    # 截图
    all_image = []
    for idx, item in enumerate(field.split(',')):
        # 为每个截图生成唯一文件名
        unique_png = os.path.join(temp_dir, f"{uuid_generate_code()}_{idx}.png")
        print2(f"Processing: {item} -> {unique_png}")

        # 添加进程锁保护Excel操作
        with excel_lock:
            excel_shot(unique_excel_path, unique_png, field=item)

        with open(unique_png, 'rb') as f:
            image = f.read()  # 读取图片为二进制
            all_image.append(base64.encodebytes(image).decode())

    # 清理临时资源
    shutil.rmtree(temp_dir, ignore_errors=True)
    print2(f"================{unique_excel_path} completed================\n\n")
    return Response(','.join(all_image))


@app.route("/to_readexlsx", methods=["POST"])
def to_readexlsx():
    data = request.files
    field = request.form.get('field')
    file = data['files']  # 获取
    # 判断是否为xlsx文件
    if not file.filename.endswith('.xlsx'):
        return "文件格式不正确\n"

    # 创建唯一临时工作目录
    temp_dir = f"temp_{uuid_generate_code()}"
    os.makedirs(temp_dir, exist_ok=True)

    # 保存文件到独立目录
    unique_excel_path = os.path.join(temp_dir, f"{uuid_generate_code()}_{file.filename}")
    file.save(unique_excel_path)
    print2(f"\n\n================{unique_excel_path}================")
    print2(field)

    # 添加进程锁保护Excel操作
    with excel_lock:
        just_open(unique_excel_path)
        with open(unique_excel_path, 'rb') as f:
            file_xlsx = f.read()  # 读取文件为二进制
            data = base64.encodebytes(file_xlsx).decode()

    # 清理临时资源
    shutil.rmtree(temp_dir, ignore_errors=True)
    print2(f"================{unique_excel_path}================\n\n")
    return Response(data)


@app.route("/get_cell_value", methods=["POST"])
def get_cell_value():
    """
    获取excel中某单元格的值
    :return:
    """
    data = request.files
    sheet_name = request.form.get('sheet_name')
    cells_list = request.form.getlist("cells")  # ["Q24","R24","S24","C24"]
    if not cells_list:
        return "未传递必要单元格位置参数cells\n"

    file = data['files']  # 获取

    # 判断是否为xlsx文件
    if not file.filename.endswith('.xlsx'):
        return "文件格式不正确\n"

    # 创建唯一临时工作目录
    temp_dir = f"temp_{uuid_generate_code()}"
    os.makedirs(temp_dir, exist_ok=True)

    # 保存文件到独立目录
    unique_excel_path = os.path.join(temp_dir, f"{uuid_generate_code()}_{file.filename}")
    file.save(unique_excel_path)

    # 添加进程锁保护Excel操作
    with excel_lock:
        just_open(unique_excel_path)
        # 打开Excel文件
        wb = load_workbook(unique_excel_path, data_only=True)
        # 获取指定的工作表
        sheet = wb[sheet_name]

        return_list = {}
        for cell in cells_list:
            row, col = parse_excel_position(cell)

            # 获取某单元格的值
            cell_value = sheet.cell(row=int(row), column=int(col)).value

            return_list[cell] = cell_value

        wb.close()

    # 清理临时资源
    shutil.rmtree(temp_dir, ignore_errors=True)
    return Response(json.dumps(return_list))


def parse_excel_position(position_str):
    position_str = position_str.strip()
    # 使用正则表达式匹配字符串中的列和行信息
    pattern = r'^([A-Z]+)(\d+)$'
    match = re.match(pattern, position_str)

    if match:
        # 提取列和行的信息，并将列转换为对应的数字
        col_str, row_str = match.groups()
        col = col_to_num(col_str)
        row = int(row_str)
        return row, col
    else:
        print2(position_str)
        raise ValueError("无效的Excel位置格式！")


def col_to_num(col_str):
    # 将Excel列字母转换为对应的数字
    col_num = 0
    for char in col_str:
        col_num = col_num * 26 + (ord(char.upper()) - ord('A')) + 1
    return col_num


def excel_shot(exl_path, img_path, sheet='', field=''):
    '''
    exl_path excel表格完整路径及名称
    img_path 图片完整路径及名称
    field 截图sheet名称 以及截图区域 中间用!分割
    '''
    if '!' not in field:
        field = f'{sheet}!{field}'

    max_retries = 5
    for attempt in range(max_retries):
        try:
            excel2img.export_img(exl_path, img_path, sheet, field)

            # 验证截图文件是否成功生成
            if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                return
            else:
                raise RuntimeError("Generated empty image file")

        except (pywintypes.com_error, OSError, RuntimeError) as e:
            wait_time = 2 ** attempt  # 指数退避重试策略
            print2(f"Attempt {attempt+1} failed, retrying in {wait_time}s: {str(e)}")

            # 每次重试前都清理Excel进程防止资源泄漏
            close_excel()
            time.sleep(wait_time)

            # 最后一次重试失败则抛出异常
            if attempt == max_retries - 1:
                raise


def just_open(filename):
    # 文件重新保存，重新保存后可读取公式生成数据
    # 注意：此函数应该在excel_lock保护下调用
    pythoncom.CoInitialize()
    xlApp = None
    xlBook = None

    try:
        from win32com.client import Dispatch
        # 使用绝对路径
        if not os.path.isabs(filename):
            path_file_name = os.path.join(curr_path, filename)
        else:
            path_file_name = filename

        xlApp = Dispatch("Excel.Application")
        print2(f"打开Excel文件: {filename}")
        xlApp.Visible = False
        xlApp.DisplayAlerts = False

        xlBook = xlApp.Workbooks.Open(path_file_name)
        xlBook.Save()
    except Exception as e:
        print2(f"Excel操作出错: {e}")
        raise
    finally:
        # 确保资源正确释放
        try:
            if xlBook:
                xlBook.Close()
            if xlApp:
                xlApp.Quit()
            pythoncom.CoUninitialize()
        except:
            pass

def uuid_generate_code():
    code = str(uuid.uuid4())
    code = code.replace('-', '')
    return code


def delete_file(file_path):
    if os.path.exists(file_path):
        os.remove(file_path)

def remove_temp():
    # 初始化时清理所有临时目录
    for temp_dir in glob.glob("temp_*"):
        shutil.rmtree(temp_dir, ignore_errors=True)

def close_excel():
    try:
        # 使用 win32com.client 关闭 Excel 应用
        excel = win32.Dispatch("Excel.Application")
        excel.Quit()
        print2("关闭 Excel 应用程序成功。")
    except Exception as e:
        print2(f"关闭 Excel 时出错: {e}")

    # 确保没有残留 Excel 进程，尝试强制关闭
    try:
        subprocess.call("taskkill /f /im excel.exe", shell=True)
        print2("强制关闭所有 Excel 进程。")
    except Exception as e:
        print2(f"强制关闭 Excel 进程时出错: {e}")

def delete_xlsx_files():
    # 查找当前目录下所有的 .xlsx 文件
    xlsx_files = glob.glob("*.xlsx")
    
    if xlsx_files:
        for file in xlsx_files:
            try:
                os.remove(file)  # 删除文件
                print2(f"已删除文件: {file}")
            except Exception as e:
                print2(f"删除文件 {file} 时出错: {e}")
    else:
        print2("当前目录下没有 .xlsx 文件")


def delete_png_files():
    # 查找当前目录下所有的 png 文件
    xlsx_files = glob.glob("*.png")
    
    if xlsx_files:
        for file in xlsx_files:
            try:
                os.remove(file)  # 删除文件
                print2(f"已删除文件: {file}")
            except Exception as e:
                print2(f"删除文件 {file} 时出错: {e}")
    else:
        print2("当前目录下没有 .png 文件")


def check_and_free_port(port):
    # 检查端口是否被占用，如果占用则终止该进程
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        s.bind(("0.0.0.0", port))
    except socket.error:
        print2(f"端口 {port} 被占用，尝试关闭占用进程...")
        try:
            # 使用 netstat 找到占用端口的 PID 并结束进程
            output = subprocess.check_output(f"netstat -ano | findstr :{port}", shell=True).decode()
            pid = int(output.strip().split()[-1])
            subprocess.call(f"taskkill /F /PID {pid}", shell=True)
            print2(f"成功结束占用端口 {port} 的进程 (PID: {pid})")
        except Exception as e:
            print2(f"关闭端口 {port} 占用进程时出错: {e}")
    finally:
        s.close()


def init():
    remove_temp()
    close_excel()
    delete_xlsx_files()
    delete_png_files()

if __name__ == '__main__':
    init()
    check_and_free_port(9999)  # 检查并释放端口
    # 使用线程模式而非进程模式，确保线程锁能正常工作
    app.run(host='0.0.0.0', port=9999, debug=False, threaded=True)