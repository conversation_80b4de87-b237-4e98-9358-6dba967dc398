﻿# -*- coding: utf-8 -*-
"""
-------------------------------------------------
@version    : v1.0
<AUTHOR> fangzheng
@contact    : <EMAIL>
@software   : PyCharm
@filename   : screenshot_service.py
@create time: 2023/6/27 12:14 PM
@modify time: 2023/6/27 12:14 PM
@describe   : windows截图服务
-------------------------------------------------
1、该服务只能部署在windows服务器上，因为需要依赖windows的dll库来实现对Excel内容截图功能

"""

import os

from flask import Flask, request, Response, jsonify
import re
import time
import uuid
import json
import base64
import pythoncom
import excel2img
import pywintypes
from openpyxl import load_workbook

# win32com.client.Dispatch('Word.Application')
app = Flask(__name__)
opd = os.path.dirname
curr_path = opd(os.path.realpath(__file__))


@app.route("/upload", methods=["POST"])
def save_file():
    data = request.files
    field = request.form.get('field')
    file = data['files']  # 获取
    # 判断是否为xlsx文件
    if not file.filename.endswith('.xlsx'):
        return "文件格式不正确\n"
    # 保存文件
    file.save(file.filename)
    print(f"\n\n================{file.filename}================")
    png_name = uuid_generate_code() + '.png'
    # 截图
    all_image = []
    for item in field.split(','):
        print(item)
        excel_shot(file.filename, png_name, field=item)
        with open(png_name, 'rb') as f:
            image = f.read()  # 读取图片为二进制
            all_image.append(base64.encodebytes(image).decode())
        os.remove(png_name)
    print(f"================{file.filename}================\n\n")
    os.remove(file.filename)
    return Response(','.join(all_image))


@app.route("/to_readexlsx", methods=["POST"])
def to_readexlsx():
    data = request.files
    field = request.form.get('field')
    file = data['files']  # 获取
    # 判断是否为xlsx文件
    if not file.filename.endswith('.xlsx'):
        return "文件格式不正确\n"
    # 保存文件
    file.save(file.filename)
    print(f"\n\n================{file.filename}================")
    print(field)
    just_open(file.filename)
    with open(file.filename, 'rb') as f:
        file_xlsx = f.read()  # 读取图片为二进制
        data = base64.encodebytes(file_xlsx).decode()
    print(f"================{file.filename}================\n\n")
    os.remove(file.filename)
    return Response(data)


@app.route("/get_cell_value", methods=["POST"])
def get_cell_value():
    """
    获取excel中某单元格的值
    :return:
    """
    data = request.files
    sheet_name = request.form.get('sheet_name')
    cells_list = request.form.getlist("cells")  # ["Q24","R24","S24","C24"]
    if not cells_list:
        return "未传递必要单元格位置参数cells\n"

    file = data['files']  # 获取
    delete_file(file.filename)

    # 判断是否为xlsx文件
    if not file.filename.endswith('.xlsx'):
        return "文件格式不正确\n"
    # 保存文件
    file.save(file.filename)
    just_open(file.filename)
    # 打开Excel文件
    wb = load_workbook(file.filename, data_only=True)
    # 获取指定的工作表
    sheet = wb[sheet_name]

    return_list = {}
    for cell in cells_list:
        row, col = parse_excel_position(cell)

        # 获取某单元格的值
        cell_value = sheet.cell(row=int(row), column=int(col)).value

        return_list[cell] = cell_value

    wb.close()
    delete_file(file.filename)
    return Response(json.dumps(return_list))


def parse_excel_position(position_str):
    position_str = position_str.strip()
    # 使用正则表达式匹配字符串中的列和行信息
    pattern = r'^([A-Z]+)(\d+)$'
    match = re.match(pattern, position_str)

    if match:
        # 提取列和行的信息，并将列转换为对应的数字
        col_str, row_str = match.groups()
        col = col_to_num(col_str)
        row = int(row_str)
        return row, col
    else:
        print(position_str)
        raise ValueError("无效的Excel位置格式！")


def col_to_num(col_str):
    # 将Excel列字母转换为对应的数字
    col_num = 0
    for char in col_str:
        col_num = col_num * 26 + (ord(char.upper()) - ord('A')) + 1
    return col_num


def excel_shot(exl_path, img_path, sheet='', field=''):
    '''
    exl_path excel表格完整路径及名称
    img_path 图片完整路径及名称
    field 截图sheet名称 以及截图区域 中间用!分割
    '''
    if '!' not in field:
        field = f'{sheet}!{field}'
    shot_fail, cnt = True, 0
    while shot_fail:
        try:
            time.sleep(10)
            excel2img.export_img(exl_path, img_path, sheet, field)
            shot_fail = False
        except (pywintypes.com_error, OSError):
            time.sleep(5)
            cnt += 1
            if cnt > 10:
                break


def just_open(filename):
    # 文件重新保存，重新保存后可读取公式生成数据
    pythoncom.CoInitialize()
    from win32com.client import Dispatch
    path_file_name = curr_path + '\\' + filename
    xlApp = Dispatch("Excel.Application")
    xlApp.Visible = True
    print(filename)
    xlBook = xlApp.Workbooks.Open(path_file_name)
    xlBook.Save()
    xlBook.Close()


def uuid_generate_code():
    code = str(uuid.uuid4())
    code = code.replace('-', '')
    return code


def delete_file(file_path):
    if os.path.exists(file_path):
        os.remove(file_path)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9999, debug=False, processes=True)
    # just_open('大湾区-区域业绩数据分析模型-2023-11-16.xlsx')
    # excel_shot(exl_path='大湾区-区域业绩数据分析模型-2023-11-16.xlsx',img_path='0.png',sheet='业绩日报院长群',field='K1:Q17')
